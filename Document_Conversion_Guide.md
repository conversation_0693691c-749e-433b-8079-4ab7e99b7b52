# Document Conversion Guide: Solar Custom-Hiring Systems Scoping Review

## Overview

This guide provides detailed instructions for converting the Solar Custom-Hiring Systems scoping review into professional LaTeX and Microsoft Word documents suitable for journal submission and academic use.

## Files Included

1. **Solar_Custom_Hiring_Scoping_Review.md** - Main article in Markdown format (~25,000 words)
2. **Solar_Custom_Hiring_Review.tex** - LaTeX template with basic structure
3. **references.bib** - Bibliography file with all citations
4. **Solar_Custom_Hiring_Review_Word.docx.md** - Word-formatted version template
5. **Figures_and_Tables.md** - All figures and tables
6. **Methodology_Appendix.md** - Detailed methodology appendix

## LaTeX Document Creation

### Prerequisites

Ensure you have a LaTeX distribution installed:
- **Windows:** MiKTeX or TeX Live
- **macOS:** MacTeX
- **Linux:** TeX Live

Required packages (most are included in standard distributions):
```latex
\usepackage{amsmath,amsfonts,amssymb}
\usepackage{graphicx}
\usepackage{booktabs}
\usepackage{longtable}
\usepackage{natbib}
\usepackage{hyperref}
\usepackage{geometry}
\usepackage{setspace}
```

### Step-by-Step LaTeX Compilation

1. **Prepare the main document:**
   - Use `Solar_Custom_Hiring_Review.tex` as the base
   - Copy all content from `Solar_Custom_Hiring_Scoping_Review.md`
   - Convert Markdown formatting to LaTeX syntax

2. **Format conversion rules:**
   ```
   Markdown → LaTeX
   # Title → \section{Title}
   ## Subtitle → \subsection{Subtitle}
   ### Subsubtitle → \subsubsection{Subsubtitle}
   **bold** → \textbf{bold}
   *italic* → \textit{italic}
   - List item → \item List item (within itemize environment)
   ```

3. **Bibliography setup:**
   - Place `references.bib` in the same directory as the .tex file
   - Ensure all citations in the text use `\citep{}` or `\citet{}` format
   - Example: `\citep{Arksey2005}` for (Arksey & O'Malley, 2005)

4. **Compilation process:**
   ```bash
   pdflatex Solar_Custom_Hiring_Review.tex
   bibtex Solar_Custom_Hiring_Review
   pdflatex Solar_Custom_Hiring_Review.tex
   pdflatex Solar_Custom_Hiring_Review.tex
   ```

5. **Table formatting:**
   Convert Markdown tables to LaTeX format:
   ```latex
   \begin{table}[H]
   \centering
   \caption{Table Title}
   \begin{tabular}{|l|c|r|}
   \hline
   Column 1 & Column 2 & Column 3 \\
   \hline
   Data 1 & Data 2 & Data 3 \\
   \hline
   \end{tabular}
   \label{tab:label}
   \end{table}
   ```

### Advanced LaTeX Features

1. **Cross-references:**
   ```latex
   \label{sec:introduction}
   As discussed in Section \ref{sec:introduction}...
   ```

2. **Figure inclusion:**
   ```latex
   \begin{figure}[H]
   \centering
   \includegraphics[width=0.8\textwidth]{figure.png}
   \caption{Figure caption}
   \label{fig:label}
   \end{figure}
   ```

3. **Long tables:**
   ```latex
   \begin{longtable}{|l|p{8cm}|}
   \caption{Long Table Title} \\
   \hline
   Column 1 & Column 2 \\
   \hline
   \endfirsthead
   \hline
   Column 1 & Column 2 \\
   \hline
   \endhead
   Data & More data \\
   \end{longtable}
   ```

## Microsoft Word Document Creation

### Method 1: Direct Conversion from Markdown

1. **Copy content from Markdown files:**
   - Start with `Solar_Custom_Hiring_Review_Word.docx.md`
   - Copy all sections from the main article
   - Paste into a new Word document

2. **Apply formatting:**
   - **Heading 1:** Main sections (Introduction, Methodology, etc.)
   - **Heading 2:** Subsections (1.1, 1.2, etc.)
   - **Heading 3:** Sub-subsections (1.1.1, 1.1.2, etc.)
   - **Font:** Times New Roman, 12pt
   - **Line spacing:** 1.5
   - **Margins:** 2.5cm (1 inch) all around

3. **Table formatting:**
   - Use Word's "Insert Table" feature
   - Apply "Table Grid" style
   - Adjust column widths as needed
   - Add table captions above tables

4. **Reference management:**
   - Use Word's built-in citation manager
   - Import references from `references.bib` using Mendeley or Zotero
   - Apply APA or journal-specific citation style

### Method 2: Using Pandoc (Recommended)

1. **Install Pandoc:**
   - Download from https://pandoc.org/installing.html
   - Install for your operating system

2. **Convert Markdown to Word:**
   ```bash
   pandoc Solar_Custom_Hiring_Scoping_Review.md -o Solar_Custom_Hiring_Review.docx --reference-doc=template.docx --bibliography=references.bib --csl=renewable-sustainable-energy-reviews.csl
   ```

3. **Create a reference template:**
   - Create `template.docx` with desired formatting
   - Set up styles for Heading 1, Heading 2, Normal text, etc.
   - Pandoc will apply these styles during conversion

### Word Document Structure

1. **Title Page:**
   - Article title (centered, bold, 14pt)
   - Author names and affiliations
   - Corresponding author information
   - Date

2. **Abstract Page:**
   - Abstract heading
   - Structured abstract (Background, Objective, Methods, Results, Conclusions)
   - Keywords

3. **Main Content:**
   - Table of Contents (auto-generated)
   - All sections with proper heading hierarchy
   - In-text citations
   - Page numbers

4. **References:**
   - Alphabetical listing
   - Consistent formatting
   - DOI links where available

## Quality Assurance Checklist

### LaTeX Document
- [ ] Document compiles without errors
- [ ] All citations resolve correctly
- [ ] Tables fit within page margins
- [ ] Figures display properly
- [ ] Cross-references work
- [ ] Bibliography is complete and formatted correctly
- [ ] Page numbers and headers are correct

### Word Document
- [ ] Consistent formatting throughout
- [ ] Table of contents updates automatically
- [ ] All headings use proper styles
- [ ] Citations and references are properly formatted
- [ ] Tables and figures are properly captioned
- [ ] Page breaks are appropriate
- [ ] Document meets journal submission requirements

## Journal-Specific Formatting

### Renewable and Sustainable Energy Reviews
- **Font:** Times New Roman, 12pt
- **Line spacing:** Double
- **Margins:** 2.5cm
- **Citation style:** Author-date (Harvard)
- **Reference format:** Alphabetical by first author
- **Abstract:** Structured, max 300 words
- **Keywords:** 5-8 keywords

### Energy Policy
- **Font:** Times New Roman, 12pt
- **Line spacing:** Double
- **Citation style:** Numbered (Vancouver)
- **Figures:** High resolution, separate files
- **Tables:** Editable format

### Energy for Sustainable Development
- **Font:** Times New Roman, 12pt
- **Line spacing:** 1.5
- **Citation style:** Author-date
- **Word limit:** 8,000-12,000 words
- **Abstract:** Unstructured, max 250 words

## Troubleshooting

### Common LaTeX Issues
1. **Bibliography not appearing:**
   - Run bibtex command
   - Check .bib file syntax
   - Ensure citations exist in text

2. **Table too wide:**
   - Use `\resizebox{\textwidth}{!}{...}`
   - Break into multiple tables
   - Use landscape orientation

3. **Figure not displaying:**
   - Check file path and extension
   - Ensure graphics package is loaded
   - Use supported formats (PDF, PNG, JPG)

### Common Word Issues
1. **Formatting inconsistencies:**
   - Use styles instead of manual formatting
   - Clear formatting and reapply styles
   - Use Format Painter for consistency

2. **Citation problems:**
   - Update citation manager
   - Check reference database
   - Manually format if necessary

3. **Table formatting:**
   - Use Table Tools for consistent formatting
   - Adjust column widths proportionally
   - Use table styles for professional appearance

## Final Steps

1. **Proofreading:**
   - Check for typos and grammatical errors
   - Verify all citations and references
   - Ensure consistent terminology

2. **Formatting review:**
   - Check adherence to journal guidelines
   - Verify figure and table quality
   - Ensure proper page layout

3. **File preparation:**
   - Save in required formats
   - Create separate figure files if needed
   - Prepare supplementary materials

4. **Submission checklist:**
   - Main manuscript file
   - Figure files (if separate)
   - Supplementary materials
   - Cover letter
   - Author information forms

## Additional Resources

- **LaTeX Help:** https://www.latex-project.org/help/
- **Pandoc Documentation:** https://pandoc.org/MANUAL.html
- **Word Styles Guide:** Microsoft Office Support
- **Citation Styles:** https://citationstyles.org/
- **Journal Guidelines:** Check specific journal websites

This comprehensive guide ensures that the Solar Custom-Hiring Systems scoping review can be professionally formatted for academic publication in either LaTeX or Word format, meeting the requirements of top-tier energy and development journals.
